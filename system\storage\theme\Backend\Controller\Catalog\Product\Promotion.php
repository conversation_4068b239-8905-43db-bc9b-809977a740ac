<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Promotion extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }
    
    /**
     * Обработва AJAX заявката за запазване на промоция
     * 
     * @param array $post POST данни от заявката
     * @return array
     */
    public function save($post) {
        $json = [];
        
        try {
            // Проверка за валидна AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }
            
            // Валидация на входните данни
            $this->validatePromotionData($post);
            
            // Зареждане на необходимите модели
            $this->loadModelAs('catalog/product', 'productModel');
            $this->loadModelAs('catalog/category', 'categoryModel');
            
            // Подготвяне на данните за промоцията
            $promotionData = $this->preparePromotionData($post);
            
            // Запазване на промоцията
            $this->savePromotion($promotionData);
            
            $json['success'] = 'Промоцията беше успешно приложена!';
            
        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }
        
        return $json;
    }
    
    /**
     * Валидира данните за промоцията
     * 
     * @param array $post
     * @throws \Exception
     */
    private function validatePromotionData($post) {
        // Проверка на процента
        if (empty($post['promotion_percentage'])) {
            throw new \Exception('Моля въведете стойност в проценти');
        }
        
        $percentage = (float)$post['promotion_percentage'];
        if ($percentage <= 0 || $percentage > 100) {
            throw new \Exception('Стойността в проценти трябва да бъде между 0 и 100');
        }
        
        // Проверка на датите
        if (empty($post['promotion_date_start']) || empty($post['promotion_date_end'])) {
            throw new \Exception('Моля въведете начална и крайна дата');
        }
        
        $startDate = strtotime($post['promotion_date_start']);
        $endDate = strtotime($post['promotion_date_end']);
        
        if ($startDate === false || $endDate === false) {
            throw new \Exception('Невалиден формат на датите');
        }
        
        if ($startDate >= $endDate) {
            throw new \Exception('Крайната дата трябва да бъде след началната');
        }
        
        if ($startDate < strtotime('today')) {
            throw new \Exception('Началната дата не може да бъде в миналото');
        }
        
        // Проверка дали има избрана категория или продукти
        $hasCategory = !empty($post['promotion_category_id']);
        $hasProducts = !empty($post['selected_products']) && is_array($post['selected_products']);
        
        if (!$hasCategory && !$hasProducts) {
            throw new \Exception('Моля изберете категория или конкретни продукти за промоцията');
        }
        
        // Валидация на категорията, ако е избрана
        if ($hasCategory) {
            $this->loadModelAs('catalog/category', 'categoryModel');
            $category = $this->categoryModel->getCategory((int)$post['promotion_category_id']);
            if (!$category) {
                throw new \Exception('Избраната категория не съществува');
            }
        }
        
        // Валидация на продуктите, ако са избрани
        if ($hasProducts) {
            $this->loadModelAs('catalog/product', 'productModel');
            foreach ($post['selected_products'] as $productId) {
                $product = $this->productModel->getProduct((int)$productId);
                if (!$product) {
                    throw new \Exception('Един от избраните продукти не съществува');
                }
            }
        }
    }
    
    /**
     * Подготвя данните за промоцията
     * 
     * @param array $post
     * @return array
     */
    private function preparePromotionData($post) {
        $data = [
            'percentage' => (float)$post['promotion_percentage'],
            'date_start' => $post['promotion_date_start'],
            'date_end' => $post['promotion_date_end'],
            'category_id' => !empty($post['promotion_category_id']) ? (int)$post['promotion_category_id'] : null,
            'product_ids' => !empty($post['selected_products']) ? array_map('intval', $post['selected_products']) : [],
            'created_by' => $this->getUserId(),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $data;
    }
    
    /**
     * Запазва промоцията в базата данни
     * 
     * @param array $data
     */
    private function savePromotion($data) {
        // Тук ще се имплементира логиката за запазване в базата данни
        // За момента ще симулираме успешно запазване
        
        // TODO: Имплементиране на реалното запазване в базата данни
        // 1. Създаване на запис в таблица за промоции
        // 2. Ако има категория - прилагане на промоцията към всички продукти в категорията
        // 3. Ако има конкретни продукти - прилагане на промоцията към тях
        // 4. Обновяване на special_price полетата в product таблицата
        
        // Симулация на запазване
        if (empty($data)) {
            throw new \Exception('Няма данни за запазване');
        }
        
        // Логиране на действието
        $this->logPromotionAction($data);
    }
    
    /**
     * Логира действието за промоция
     * 
     * @param array $data
     */
    private function logPromotionAction($data) {
        $logMessage = sprintf(
            'Приложена промоция: %s%% отстъпка от %s до %s',
            $data['percentage'],
            $data['date_start'],
            $data['date_end']
        );
        
        if ($data['category_id']) {
            $logMessage .= ' за категория ID: ' . $data['category_id'];
        }
        
        if (!empty($data['product_ids'])) {
            $logMessage .= ' за продукти: ' . implode(', ', $data['product_ids']);
        }
        
        // TODO: Запазване в лог файл или база данни
        error_log($logMessage);
    }
    
    /**
     * Получава ID на текущия потребител
     * 
     * @return int
     */
    private function getUserId() {
        // TODO: Имплементиране на получаване на потребителския ID
        return 1; // Временно
    }
}
