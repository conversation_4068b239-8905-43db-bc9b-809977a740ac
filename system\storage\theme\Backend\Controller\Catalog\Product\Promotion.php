<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Promotion extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }
    
    /**
     * Обработва AJAX заявката за запазване на промоция
     * 
     * @param array $post POST данни от заявката
     * @return array
     */
    public function save($post) {
        $json = [];

        try {
            // Проверка за валидна AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }
            
            // Валидация на входните данни
            $this->validatePromotionData($post);

            
            // Подготвяне на данните за промоцията
            $promotionData = $this->preparePromotionData($post);
            
            // Запазване на промоцията
            $this->savePromotion($promotionData);
            
            $json['success'] = 'Промоцията беше успешно приложена!';
            
        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }
        
        return $json;
    }
    
    /**
     * Валидира данните за промоцията
     *
     * @param array $post
     * @throws \Exception
     */
    private function validatePromotionData($post) {
        // Проверка на стойността
        if (empty($post['promotion_value'])) {
            throw new \Exception('Моля въведете стойност');
        }

        $value = (float)$post['promotion_value'];
        if ($value <= 0) {
            throw new \Exception('Стойността трябва да бъде по-голяма от 0');
        }

        // Проверка на типа
        if (empty($post['promotion_type']) || !in_array($post['promotion_type'], ['percentage', 'fixed'])) {
            throw new \Exception('Невалиден тип промоция');
        }

        // Специфична валидация за процент
        if ($post['promotion_type'] === 'percentage' && $value > 100) {
            throw new \Exception('Процентът не може да бъде по-голям от 100');
        }
        
        // Проверка на датите - поне една дата трябва да бъде въведена
        $hasStartDate = !empty($post['promotion_date_start']);
        $hasEndDate = !empty($post['promotion_date_end']);

        if (!$hasStartDate && !$hasEndDate) {
            throw new \Exception('Моля въведете поне една дата (начална или крайна)');
        }

        $startDate = null;
        $endDate = null;

        // Валидация на началната дата, ако е въведена
        if ($hasStartDate) {
            $startDate = strtotime($post['promotion_date_start']);
            if ($startDate === false) {
                throw new \Exception('Невалиден формат на началната дата');
            }
            if ($startDate < strtotime('today')) {
                throw new \Exception('Началната дата не може да бъде в миналото');
            }
        }

        // Валидация на крайната дата, ако е въведена
        if ($hasEndDate) {
            $endDate = strtotime($post['promotion_date_end']);
            if ($endDate === false) {
                throw new \Exception('Невалиден формат на крайната дата');
            }
        }

        // Ако и двете дати са въведени, проверяваме реда им
        if ($hasStartDate && $hasEndDate && $startDate >= $endDate) {
            throw new \Exception('Крайната дата трябва да бъде след началната');
        }
        
        // Проверка дали има избрана категория или продукти
        $hasCategory = !empty($post['promotion_category_id']);
        $hasProducts = !empty($post['promotion_products']);

        if (!$hasCategory && !$hasProducts) {
            throw new \Exception('Моля изберете категория или конкретни продукти за промоцията');
        }

        $this->loadModelsAs([
                'catalog/product' => 'productModel',
                'catalog/category' => 'categoryModel'
            ]);

        // Валидация на категорията, ако е избрана
        if ($hasCategory) {    
            $category = $this->categoryModel->getCategory((int)$post['promotion_category_id']);
            if (!$category) {
                throw new \Exception('Избраната категория не съществува');
            }
        }

        // Валидация на продуктите, ако са избрани
        if ($hasProducts) {
            $productIds = json_decode($post['promotion_products'], true);
            if (!is_array($productIds)) {
                throw new \Exception('Невалиден формат на продуктите');
            }

            foreach ($productIds as $productId) {
                $product = $this->productModel->getProduct((int)$productId);
                if (!$product) {
                    throw new \Exception('Един от избраните продукти не съществува');
                }
            }
        }
    }
    
    /**
     * Подготвя данните за промоцията
     *
     * @param array $post
     * @return array
     */
    private function preparePromotionData($post) {
        $data = [
            'value' => (float)$post['promotion_value'],
            'type' => $post['promotion_type'],
            'date_start' => !empty($post['promotion_date_start']) ? $post['promotion_date_start'] : null,
            'date_end' => !empty($post['promotion_date_end']) ? $post['promotion_date_end'] : null,
            'category_id' => !empty($post['promotion_category_id']) ? (int)$post['promotion_category_id'] : null,
            'product_ids' => !empty($post['promotion_products']) ? json_decode($post['promotion_products'], true) : [],
            'created_by' => $this->getUserId(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        return $data;
    }
    
    /**
     * Запазва промоцията в базата данни
     *
     * @param array $data
     */
    private function savePromotion($data) {
        if (empty($data)) {
            throw new \Exception('Няма данни за запазване');
        }

        try {
            // Започваме транзакция
            $this->db->query("START TRANSACTION");

            // 1. Създаване на запис в таблица за промоции (ако съществува)
            $this->createPromotionRecord($data);

            // 2. Прилагане на промоцията към продуктите
            if ($data['category_id']) {
                // Прилагане към всички продукти в категорията
                $this->applyPromotionToCategory($data);
            }

            if (!empty($data['product_ids'])) {
                // Прилагане към конкретни продукти
                $this->applyPromotionToProducts($data);
            }

            // Потвърждаваме транзакцията
            $this->db->query("COMMIT");

            // Логиране на действието
            $this->logPromotionAction($data);

        } catch (\Exception $e) {
            // Отменяме транзакцията при грешка
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * Създава запис за промоцията в таблица за промоции (ако съществува)
     *
     * @param array $data
     */
    private function createPromotionRecord($data) {
        // Проверяваме дали съществува таблица за промоции
        $tableExists = $this->db->query("SHOW TABLES LIKE '" . DB_PREFIX . "promotions'");

        if ($tableExists->num_rows > 0) {
            $sql = "INSERT INTO `" . DB_PREFIX . "promotions` SET
                    `type` = '{$data['type']}',
                    `value` = '{$data['value']}',
                    `date_start` = " . ($data['date_start'] ? "'{$data['date_start']}'" : "NULL") . ",
                    `date_end` = " . ($data['date_end'] ? "'{$data['date_end']}'" : "NULL") . ",
                    `category_id` = " . ($data['category_id'] ? "'{$data['category_id']}'" : "NULL") . ",
                    `product_ids` = '" . (!empty($data['product_ids']) ? json_encode($data['product_ids']) : '') . "',
                    `created_by` = '{$data['created_by']}',
                    `created_at` = '{$data['created_at']}',
                    `status` = '1'";

            $this->db->query($sql);
        }
    }

    /**
     * Прилага промоцията към всички продукти в категорията
     *
     * @param array $data
     */
    private function applyPromotionToCategory($data) {
        // Получаваме всички продукти в категорията
        $sql = "SELECT p.product_id, p.price
                FROM `" . DB_PREFIX . "product` p
                LEFT JOIN `" . DB_PREFIX . "product_to_category` ptc ON (p.product_id = ptc.product_id)
                WHERE ptc.category_id = '{$data['category_id']}'
                AND p.status = '1'";

        $result = $this->db->query($sql);

        foreach ($result->rows as $product) {
            $this->applyPromotionToProduct($product['product_id'], $product['price'], $data);
        }
    }

    /**
     * Прилага промоцията към конкретни продукти
     *
     * @param array $data
     */
    private function applyPromotionToProducts($data) {
        foreach ($data['product_ids'] as $productId) {
            // Получаваме цената на продукта
            $sql = "SELECT price FROM `" . DB_PREFIX . "product` WHERE product_id = '{$productId}'";
            $result = $this->db->query($sql);

            if ($result->num_rows > 0) {
                $this->applyPromotionToProduct($productId, $result->row['price'], $data);
            }
        }
    }

    /**
     * Прилага промоцията към конкретен продукт
     *
     * @param int $productId
     * @param float $originalPrice
     * @param array $data
     */
    private function applyPromotionToProduct($productId, $originalPrice, $data) {
        // Изчисляваме промоционалната цена
        if ($data['type'] === 'percentage') {
            $specialPrice = $originalPrice * (1 - $data['value'] / 100);
        } else {
            $specialPrice = max(0, $originalPrice - $data['value']);
        }

        // Обновяваме product_special таблицата
        $sql = "INSERT INTO `" . DB_PREFIX . "product_special` SET
                `product_id` = '{$productId}',
                `customer_group_id` = '1',
                `priority` = '1',
                `price` = '{$specialPrice}',
                `date_start` = " . ($data['date_start'] ? "'{$data['date_start']}'" : "'0000-00-00'") . ",
                `date_end` = " . ($data['date_end'] ? "'{$data['date_end']}'" : "'0000-00-00'") . "
                ON DUPLICATE KEY UPDATE
                `price` = '{$specialPrice}',
                `date_start` = " . ($data['date_start'] ? "'{$data['date_start']}'" : "'0000-00-00'") . ",
                `date_end` = " . ($data['date_end'] ? "'{$data['date_end']}'" : "'0000-00-00'") . "";

        $this->db->query($sql);
    }

    /**
     * Логира действието за промоция
     *
     * @param array $data
     */
    private function logPromotionAction($data) {
        $discountText = $data['type'] === 'percentage'
            ? $data['value'] . '% отстъпка'
            : $data['value'] . ' лв. отстъпка';

        // Формираме съобщението за периода
        $periodText = '';
        if ($data['date_start'] && $data['date_end']) {
            $periodText = "от {$data['date_start']} до {$data['date_end']}";
        } elseif ($data['date_start']) {
            $periodText = "от {$data['date_start']}";
        } elseif ($data['date_end']) {
            $periodText = "до {$data['date_end']}";
        }

        $logMessage = sprintf(
            'Приложена промоция: %s %s',
            $discountText,
            $periodText
        );

        if ($data['category_id']) {
            $logMessage .= ' за категория ID: ' . $data['category_id'];
        }

        if (!empty($data['product_ids'])) {
            $logMessage .= ' за продукти: ' . implode(', ', $data['product_ids']);
        }

        F()->log->developer($logMessage, __FILE__, __LINE__);
    }
    
    /**
     * Получава ID на текущия потребител
     * 
     * @return int
     */
    private function getUserId() {
        // TODO: Имплементиране на получаване на потребителския ID
        return 1; // Временно
    }
}
