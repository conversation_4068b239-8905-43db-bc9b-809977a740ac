/**
 * JavaScript модул за филтриране на продукти
 * Отговаря за автодопълване на категории и обработка на филтри
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initProductFilter();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {
            
            /**
             * Инициализация на модула за филтриране на продукти
             */
            initProductFilter: function() {
                this.setupProductFilterModal();
                this.initCategoryAutocomplete();
                this.populateFilterFieldsFromURL();
            },

            /**
             * Настройка на филтър модала за продукти
             */
            setupProductFilterModal: function() {
                const filterBtn = document.getElementById('filter-btn');
                const filterModal = document.getElementById('filter-modal');
                const closeFilter = document.getElementById('close-filter');
                const filterForm = document.getElementById('filter-form');
                const resetFilter = document.getElementById('reset-filter');

                if (filterBtn && filterModal) {
                    filterBtn.addEventListener('click', () => {
                        this.openProductFilterModal(filterModal);
                    });
                }

                if (closeFilter && filterModal) {
                    closeFilter.addEventListener('click', () => {
                        this.closeProductFilterModal(filterModal);
                    });
                }

                if (filterModal) {
                    filterModal.addEventListener('click', (e) => {
                        if (e.target === filterModal) {
                            this.closeProductFilterModal(filterModal);
                        }
                    });
                }

                if (filterForm) {
                    filterForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleProductFilterSubmit(filterForm);
                        if (filterModal) {
                            this.closeProductFilterModal(filterModal);
                        }
                    });
                }

                if (resetFilter && filterForm) {
                    resetFilter.addEventListener('click', () => {
                        this.clearProductFilters();
                    });
                }
            },

            /**
             * Инициализация на автодопълването за категории
             * Копирано от categories.js и адаптирано за продуктите
             */
            initCategoryAutocomplete: function() {
                const categoryInput = document.getElementById('filter-category');
                const categorySuggestions = document.getElementById('filter-category-suggestions');
                const categoryIdInput = document.getElementById('filter-category-id');

                if (!categoryInput || !categorySuggestions) {
                    return;
                }

                // Проверяваме дали вече има event listeners
                if (categoryInput.dataset.listenerAttached === 'true') {
                    return;
                }

                // Loading indicator за autocomplete
                let loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'autocomplete-loading p-2 text-gray-500 text-sm';
                loadingIndicator.textContent = 'Зареждане...';
                loadingIndicator.style.display = 'none';
                categorySuggestions.appendChild(loadingIndicator);

                let debounceTimer;
                let currentRequest = null;

                const fetchAndDisplaySuggestions = (query) => {
                    // Отменяме предишната заявка, ако има такава
                    if (currentRequest) {
                        currentRequest.abort();
                    }

                    const controller = new AbortController();
                    currentRequest = controller;

                    // Показваме loading индикатор
                    loadingIndicator.style.display = 'block';
                    categorySuggestions.classList.remove('hidden');

                    // Изчистваме старите предложения
                    const oldSuggestions = categorySuggestions.querySelector('.suggestions-list');
                    if (oldSuggestions) {
                        oldSuggestions.remove();
                    }

                    const userToken = this.getUserToken();
                    const timestamp = Date.now();
                    const currentQuery = query.trim();

                    // При празно поле - максимум 10 резултата
                    // При активно търсене - без ограничение (използваме голям лимит)
                    const limit = currentQuery === '' ? 10 : 1000;

                    // Използваме същия endpoint като в categories.js
                    let fetchUrl = `index.php?route=catalog/category/autocomplete&filter_name=${encodeURIComponent(currentQuery)}&limit=${limit}&user_token=${userToken}&_=${timestamp}`;

                    fetch(fetchUrl, {
                        signal: controller.signal,
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Грешка при зареждане на категории');
                        return response.json();
                    })
                    .then(data => {
                        loadingIndicator.style.display = 'none';

                        const suggestionsList = document.createElement('div');
                        suggestionsList.className = 'suggestions-list absolute w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 z-10 max-h-60 overflow-y-auto';

                        if (data.length > 0) {
                            data.forEach(category => {
                                const item = document.createElement('div');
                                item.className = 'p-2 cursor-pointer hover:bg-gray-100 autocomplete-suggestion';
                                item.textContent = category.name;
                                item.dataset.id = category.category_id;
                                item.addEventListener('click', () => {
                                    this.selectCategory(category.category_id, category.name);
                                });
                                suggestionsList.appendChild(item);
                            });
                        } else {
                            const noResults = document.createElement('div');
                            noResults.className = 'p-2 text-gray-500 autocomplete-no-results';
                            noResults.textContent = currentQuery ? 'Няма намерени категории' : 'Няма предложения за категории';
                            suggestionsList.appendChild(noResults);
                        }

                        categorySuggestions.appendChild(suggestionsList);
                        categorySuggestions.classList.remove('hidden');
                    })
                    .catch(error => {
                        loadingIndicator.style.display = 'none';
                        if (error.name !== 'AbortError') {
                            console.error('Грешка при зареждане на категории:', error);
                        }
                    });
                };

                // Event listeners за input полето
                categoryInput.addEventListener('input', (e) => {
                    const query = e.target.value;
                    
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        fetchAndDisplaySuggestions(query);
                    }, 300);
                });

                categoryInput.addEventListener('focus', () => {
                    const query = categoryInput.value;
                    if (query.trim() === '') {
                        fetchAndDisplaySuggestions('');
                    } else {
                        fetchAndDisplaySuggestions(query);
                    }
                });

                categoryInput.addEventListener('keydown', (e) => {
                    const suggestions = categorySuggestions.querySelectorAll('.autocomplete-suggestion');
                    if (suggestions.length === 0 || categorySuggestions.classList.contains('hidden')) return;

                    let activeIndex = Array.from(suggestions).findIndex(s => s.classList.contains('active'));

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = (activeIndex + 1) % suggestions.length;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (activeIndex >= 0) suggestions[activeIndex].classList.remove('active');
                        activeIndex = activeIndex <= 0 ? suggestions.length - 1 : activeIndex - 1;
                        suggestions[activeIndex].classList.add('active');
                        suggestions[activeIndex].scrollIntoView({ block: 'nearest' });
                    } else if (e.key === 'Enter') {
                        e.preventDefault();
                        if (activeIndex >= 0) {
                            const selectedSuggestion = suggestions[activeIndex];
                            this.selectCategory(selectedSuggestion.dataset.id, selectedSuggestion.textContent);
                        }
                    } else if (e.key === 'Escape') {
                        categorySuggestions.classList.add('hidden');
                    }
                });

                // Скриваме предложенията при клик извън тях
                document.addEventListener('click', (e) => {
                    if (!categoryInput.contains(e.target) && !categorySuggestions.contains(e.target)) {
                        categorySuggestions.classList.add('hidden');
                    }
                });

                categoryInput.dataset.listenerAttached = 'true';
            },

            /**
             * Избира категория от автодопълването
             */
            selectCategory: function(categoryId, categoryName) {
                const categoryInput = document.getElementById('filter-category');
                const categorySuggestions = document.getElementById('filter-category-suggestions');
                const categoryIdInput = document.getElementById('filter-category-id');

                if (categoryInput) {
                    categoryInput.value = categoryName;
                }
                if (categoryIdInput) {
                    categoryIdInput.value = categoryId;
                }
                if (categorySuggestions) {
                    categorySuggestions.classList.add('hidden');
                }
            },

            /**
             * Обработва изпращането на филтър формата
             */
            handleProductFilterSubmit: function(form) {
                const formData = new FormData(form);
                const params = new URLSearchParams();

                // Добавяме всички полета от формата към URL параметрите
                for (let [key, value] of formData.entries()) {
                    if (value.trim() !== '') {
                        params.append(key, value);
                    }
                }

                // Добавяне на user_token
                const userToken = this.getUserToken();
                if (userToken) {
                    params.append('user_token', userToken);
                }

                // Пренасочваме към страницата с филтрите
                const baseUrl = 'index.php?route=catalog/product';
                const url = baseUrl + (params.toString() ? '&' + params.toString() : '');
                window.location.href = url;
            },

            /**
             * Изчиства всички филтри
             */
            clearProductFilters: function() {
                // Пренасочваме към основната страница без параметри
                const userToken = this.getUserToken();
                const url = `index.php?route=catalog/product&user_token=${userToken}`;
                window.location.href = url;
            },

            /**
             * Попълва полетата на филтъра от URL параметрите
             */
            populateFilterFieldsFromURL: function() {
                const urlParams = new URLSearchParams(window.location.search);
                
                // Мапинг на URL параметри към полета
                const fieldMapping = {
                    'filter_name': 'filter_name',
                    'filter_model': 'filter_model',
                    'filter_category_id': 'filter_category_id',
                    'filter_price_min': 'filter_price_min',
                    'filter_price_max': 'filter_price_max',
                    'filter_status': 'filter_status',
                    'filter_date_start': 'filter_date_start',
                    'filter_date_end': 'filter_date_end'
                };

                // Попълваме полетата
                for (let [urlParam, fieldName] of Object.entries(fieldMapping)) {
                    const value = urlParams.get(urlParam);
                    if (value) {
                        const field = document.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            field.value = value;
                        }
                    }
                }

                // Специална обработка за категорията - трябва да покажем името
                const categoryId = urlParams.get('filter_category_id');
                if (categoryId) {
                    this.loadCategoryName(categoryId);
                }
            },

            /**
             * Зарежда името на категорията по ID
             */
            loadCategoryName: function(categoryId) {
                const userToken = this.getUserToken();
                const url = `index.php?route=catalog/category/autocomplete&filter_name=&user_token=${userToken}`;
                
                fetch(url)
                .then(response => response.json())
                .then(data => {
                    const category = data.find(cat => cat.category_id == categoryId);
                    if (category) {
                        const categoryInput = document.getElementById('filter-category');
                        if (categoryInput) {
                            categoryInput.value = category.name;
                        }
                    }
                })
                .catch(error => {
                    console.error('Грешка при зареждане на името на категорията:', error);
                });
            },

            /**
             * Отваряне на филтър модал
             */
            openProductFilterModal: function(modal) {
                if (modal) {
                    modal.classList.remove('hidden');
                    modal.classList.add('flex');
                    document.body.style.overflow = 'hidden';
                }
            },

            /**
             * Затваряне на филтър модал
             */
            closeProductFilterModal: function(modal) {
                if (modal) {
                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    document.body.style.overflow = '';
                }
            }
        });
    }
})();
