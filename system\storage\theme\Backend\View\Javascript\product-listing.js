/**
 * Модул за управление на продуктите - листинг страница
 * Включва филтриране, превключване на изгледи и синхронизация на филтри с бутоните Активни/Промоции
 */
(function() {
    'use strict';

    // Основен модул за продуктите
    const ProductListing = {
        
        /**
         * Инициализация на модула
         */
        init: function() {
            this.initFilterModal();
            this.initViewToggle();
            this.initProductActions();
            this.initActivePromotionsToggle();
            this.initProductImages();
            this.initStatusSync();

            // Попълваме филтрите от URL при зареждане
            if (typeof ProductFilter !== 'undefined') {
                ProductFilter.populateFilterFieldsFromURL();
            }
        },

        /**
         * Инициализация на филтърния модал
         */
        initFilterModal: function() {
            const filterBtn = document.getElementById('filter-btn');
            const filterModal = document.getElementById('filter-modal');
            const closeFilter = document.getElementById('close-filter');
            const filterForm = document.getElementById('filter-form');
            const resetFilter = document.getElementById('reset-filter');

            if (!filterBtn || !filterModal) return;

            filterBtn.addEventListener('click', function() {
                filterModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });

            if (closeFilter) {
                closeFilter.addEventListener('click', function() {
                    filterModal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                });
            }

            filterModal.addEventListener('click', function(e) {
                if (e.target === filterModal) {
                    filterModal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }
            });

            if (filterForm) {
                filterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Използваме ProductFilter модула за обработка на формата
                    if (typeof ProductFilter !== 'undefined') {
                        ProductFilter.handleProductFilterSubmit(e);
                    } else {
                        // Fallback логика
                        filterModal.classList.add('hidden');
                        document.body.style.overflow = 'auto';
                    }
                });
            }

            if (resetFilter) {
                resetFilter.addEventListener('click', function() {
                    if (typeof ProductFilter !== 'undefined') {
                        ProductFilter.clearProductFilters();
                    } else {
                        // Fallback логика
                        if (filterForm) filterForm.reset();
                    }
                });
            }
        },

        /**
         * Инициализация на превключването между grid и list изглед
         */
        initViewToggle: function() {
            const gridView = document.getElementById('grid-view');
            const listView = document.getElementById('list-view');
            const productsContainer = document.getElementById('products-grid');

            if (!gridView || !listView || !productsContainer) return;

            gridView.addEventListener('click', function() {
                ProductListing.switchToGridView(productsContainer, gridView, listView);
            });

            listView.addEventListener('click', function() {
                ProductListing.switchToListView(productsContainer, gridView, listView);
            });
        },

        /**
         * Превключване към grid изглед
         */
        switchToGridView: function(container, gridBtn, listBtn) {
            // Премахваме list класовете
            container.classList.remove('grid-cols-1');

            // Добавяме grid класовете
            container.classList.add('grid-cols-1', 'sm:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4', '2xl:grid-cols-5');

            // Обновяваме бутоните
            gridBtn.classList.add('active', 'bg-gray-100');
            listBtn.classList.remove('active', 'bg-gray-100');

            // Обновяваме изображенията и layout
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                const img = card.querySelector('img');
                const imageContainer = card.querySelector('.product-image-container');
                const contentDiv = card.querySelector('.product-content');
                const statusBadges = card.querySelector('.absolute.top-2.right-2');

                // Възстановяваме grid layout
                card.classList.remove('flex-row', 'relative');
                card.classList.add('flex-col');

                if (imageContainer) {
                    imageContainer.classList.remove('w-32', 'h-full', 'max-h-40', 'rounded-l', 'flex-shrink-0');
                    imageContainer.classList.add('w-full', 'h-48', 'rounded-t');
                    imageContainer.style.minHeight = '';
                }

                if (img) {
                    img.classList.remove('w-full', 'h-full', 'max-h-40', 'rounded-l', 'object-cover');
                    img.classList.add('w-full', 'h-48', 'rounded-t');
                }

                if (contentDiv) {
                    contentDiv.classList.remove('flex-1', 'ml-4');
                }

                // Възстановяваме статус баджовете в оригиналната им позиция
                if (statusBadges) {
                    statusBadges.classList.remove('absolute', 'top-2', 'right-2', 'z-10');
                    statusBadges.classList.add('absolute', 'top-3', 'right-3');

                    // Връщаме баджовете в image container
                    const imageRelativeContainer = imageContainer ? imageContainer.parentElement : null;
                    if (imageRelativeContainer) {
                        imageRelativeContainer.classList.add('relative');
                        imageRelativeContainer.appendChild(statusBadges);
                    }
                }
            });
        },

        /**
         * Превключване към list изглед
         */
        switchToListView: function(container, gridBtn, listBtn) {
            // Премахваме grid класовете
            container.classList.remove('sm:grid-cols-2', 'lg:grid-cols-3', 'xl:grid-cols-4', '2xl:grid-cols-5');
            container.classList.add('grid-cols-1');

            // Обновяваме бутоните
            listBtn.classList.add('active', 'bg-gray-100');
            gridBtn.classList.remove('active', 'bg-gray-100');

            // Обновяваме изображенията и layout
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                const img = card.querySelector('img');
                const imageContainer = card.querySelector('.product-image-container');
                const contentDiv = card.querySelector('.product-content');
                const statusBadges = card.querySelector('.absolute.top-3.right-3');

                // Превключваме към list layout
                card.classList.remove('flex-col');
                card.classList.add('flex-row', 'relative');

                // Настройваме изображението да заема цялата височина до максимум 160px
                if (imageContainer) {
                    imageContainer.classList.remove('w-full', 'h-48', 'rounded-t');
                    imageContainer.classList.add('w-32', 'h-full', 'max-h-40', 'rounded-l', 'flex-shrink-0');
                    imageContainer.style.minHeight = '120px';
                }

                if (img) {
                    img.classList.remove('w-full', 'h-48', 'rounded-t');
                    img.classList.add('w-full', 'h-full', 'max-h-40', 'rounded-l', 'object-cover');
                }

                if (contentDiv) {
                    contentDiv.classList.add('flex-1', 'ml-4');
                }

                // Преместваме статус баджовете в горния десен ъгъл на целия контейнер
                if (statusBadges) {
                    statusBadges.classList.remove('absolute', 'top-3', 'right-3');
                    statusBadges.classList.add('absolute', 'top-2', 'right-2', 'z-10');
                    // Премахваме от image container и добавяме към main card
                    const imageRelativeContainer = imageContainer ? imageContainer.parentElement : null;
                    if (imageRelativeContainer && imageRelativeContainer.classList.contains('relative')) {
                        imageRelativeContainer.classList.remove('relative');
                        card.appendChild(statusBadges);
                    }
                }
            });
        },

        /**
         * Инициализация на действията с продукти
         */
        initProductActions: function() {
            // Dropdown менюта за действия
            const productActionsBtns = document.querySelectorAll('.product-actions-btn');
            productActionsBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const dropdown = this.nextElementSibling;
                    if (dropdown) {
                        dropdown.classList.toggle('hidden');
                    }
                });
            });

            // Затваряне на dropdown при клик извън тях
            document.addEventListener('click', function() {
                document.querySelectorAll('.product-actions-dropdown').forEach(dropdown => {
                    dropdown.classList.add('hidden');
                });
            });

            // Потвърждение за изтриване
            const deleteButtons = document.querySelectorAll('.delete-product');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-product-id');
                    if (confirm('Сигурни ли сте, че искате да изтриете този продукт?')) {
                        // Използваме глобалната променлива за URL
                        if (typeof deleteUrl !== 'undefined') {
                            window.location = deleteUrl.replace('PRODUCT_ID', productId);
                        }
                    }
                });
            });
        },

        /**
         * Инициализация на toggle бутоните за Активни/Промоции с запазване на филтрите
         */
        initActivePromotionsToggle: function() {
            const filterActiveCheckbox = document.getElementById('filter-active');
            const filterSpecialCheckbox = document.getElementById('filter-special');

            if (filterActiveCheckbox) {
                filterActiveCheckbox.addEventListener('change', function() {
                    ProductListing.handleToggleWithFilters('filter_status', this.checked ? '1' : '');
                });
            }

            if (filterSpecialCheckbox) {
                filterSpecialCheckbox.addEventListener('change', function() {
                    ProductListing.handleToggleWithFilters('filter_special', this.checked ? '1' : '');
                });
            }
        },

        /**
         * Обработка на toggle бутоните със запазване на текущите филтри
         */
        handleToggleWithFilters: function(toggleParam, toggleValue) {
            const currentUrl = new URL(window.location);
            const params = new URLSearchParams(currentUrl.search);

            // Запазваме всички текущи филтри и параметри
            const preservedFilters = [
                'route',
                'filter_name',
                'filter_model',
                'filter_category_id',
                'filter_price_min',
                'filter_price_max',
                'filter_date_start',
                'filter_date_end',
                'filter_status',
                'filter_special',
                'sort',
                'order',
                'page'
            ];

            // Създаваме нов URL със запазените филтри
            const newParams = new URLSearchParams();

            // Запазваме всички съществуващи параметри
            preservedFilters.forEach(filter => {
                const value = params.get(filter);
                if (value && value !== '') {
                    newParams.set(filter, value);
                }
            });

            // ВАЖНО: Запазваме user_token от текущия URL
            const userToken = params.get('user_token');
            if (userToken) {
                newParams.set('user_token', userToken);
            }

            // Обновяваме конкретния toggle параметър
            if (toggleValue && toggleValue !== '') {
                newParams.set(toggleParam, toggleValue);

                // Синхронизираме статуса във филтърната форма
                if (toggleParam === 'filter_status') {
                    this.syncFilterStatusField(toggleValue);
                }
            } else {
                newParams.delete(toggleParam);

                // Изчистваме статуса във филтърната форма
                if (toggleParam === 'filter_status') {
                    this.syncFilterStatusField('');
                }
            }

            // Премахваме page параметъра за да започнем от първа страница
            newParams.delete('page');

            // Пренасочваме към новия URL
            const baseUrl = currentUrl.pathname;
            const queryString = newParams.toString();

            console.log('Toggle Debug:', {
                toggleParam: toggleParam,
                toggleValue: toggleValue,
                baseUrl: baseUrl,
                queryString: queryString,
                finalUrl: baseUrl + (queryString ? '?' + queryString : ''),
                preservedParams: Object.fromEntries(newParams),
                originalRoute: params.get('route'),
                hasUserToken: !!params.get('user_token')
            });

            window.location = baseUrl + (queryString ? '?' + queryString : '');
        },

        /**
         * Синхронизира полето за статус във филтърната форма
         */
        syncFilterStatusField: function(statusValue) {
            const filterStatusField = document.querySelector('select[name="filter_status"]');
            if (filterStatusField) {
                filterStatusField.value = statusValue;
            }
        },

        /**
         * Инициализация на модула за изображения
         */
        initProductImages: function() {
            if (typeof ProductImages !== 'undefined') {
                ProductImages.initProductListImages();
            }
        },

        /**
         * Инициализация на синхронизацията на статуса
         */
        initStatusSync: function() {
            // Синхронизираме статуса при зареждане на страницата
            const currentUrl = new URL(window.location);
            const params = new URLSearchParams(currentUrl.search);
            const filterStatus = params.get('filter_status');
            const filterSpecial = params.get('filter_special');

            // Синхронизираме filter_status
            if (filterStatus) {
                this.syncFilterStatusField(filterStatus);

                // Синхронизираме и toggle бутона за активни
                const filterActiveCheckbox = document.getElementById('filter-active');
                if (filterActiveCheckbox) {
                    filterActiveCheckbox.checked = (filterStatus === '1');
                }
            }

            // Синхронизираме filter_special
            if (filterSpecial) {
                // Синхронизираме toggle бутона за промоции
                const filterSpecialCheckbox = document.getElementById('filter-special');
                if (filterSpecialCheckbox) {
                    filterSpecialCheckbox.checked = (filterSpecial === '1');
                }
            }
        }
    };

    // Разширяваме глобалния обект ако съществува
    if (typeof window.ProductListing !== 'undefined') {
        Object.assign(window.ProductListing, ProductListing);
    } else {
        window.ProductListing = ProductListing;
    }

    // Автоматична инициализация при зареждане на страницата
    document.addEventListener('DOMContentLoaded', function() {
        ProductListing.init();
    });

})();
