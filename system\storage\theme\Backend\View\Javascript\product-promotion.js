/**
 * JavaScript модул за управление на промоции
 * Отговаря за модала за промоции, автодопълване и AJAX заявки
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initProductPromotion();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {
            
            // Избрани продукти за промоцията
            selectedProducts: [],
            
            /**
             * Инициализация на модула за промоции
             */
            initProductPromotion: function() {
                this.setupPromotionModal();
                this.initPromotionCategoryAutocomplete();
                this.initPromotionProductAutocomplete();
                this.setupPromotionForm();
            },

            /**
             * Настройка на промоция модала
             */
            setupPromotionModal: function() {
                const promotionBtn = document.getElementById('promotion-btn');
                const promotionModal = document.getElementById('promotion-modal');
                const closePromotion = document.getElementById('close-promotion');
                const cancelPromotion = document.getElementById('cancel-promotion');

                if (promotionBtn && promotionModal) {
                    promotionBtn.addEventListener('click', () => {
                        this.openPromotionModal(promotionModal);
                    });
                }

                if (closePromotion && promotionModal) {
                    closePromotion.addEventListener('click', () => {
                        this.closePromotionModal(promotionModal);
                    });
                }

                if (cancelPromotion && promotionModal) {
                    cancelPromotion.addEventListener('click', () => {
                        this.closePromotionModal(promotionModal);
                    });
                }

                if (promotionModal) {
                    promotionModal.addEventListener('click', (e) => {
                        if (e.target === promotionModal) {
                            this.closePromotionModal(promotionModal);
                        }
                    });
                }
            },

            /**
             * Инициализация на автодопълването за категории в промоциите
             */
            initPromotionCategoryAutocomplete: function() {
                const categoryInput = document.getElementById('promotion-category');
                const categorySuggestions = document.getElementById('promotion-category-suggestions');
                const categoryIdInput = document.getElementById('promotion-category-id');

                if (!categoryInput || !categorySuggestions) {
                    return;
                }

                // Проверяваме дали вече има event listeners
                if (categoryInput.dataset.listenerAttached === 'true') {
                    return;
                }

                // Loading indicator за autocomplete
                let loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'autocomplete-loading p-2 text-gray-500 text-sm';
                loadingIndicator.textContent = 'Зареждане...';
                loadingIndicator.style.display = 'none';
                categorySuggestions.appendChild(loadingIndicator);

                let debounceTimer;
                let currentRequest = null;

                const fetchAndDisplaySuggestions = (query) => {
                    // Отменяме предишната заявка, ако има такава
                    if (currentRequest) {
                        currentRequest.abort();
                    }

                    const controller = new AbortController();
                    currentRequest = controller;

                    // Показваме loading индикатор
                    loadingIndicator.style.display = 'block';
                    categorySuggestions.classList.remove('hidden');

                    // Изчистваме старите предложения
                    const oldSuggestions = categorySuggestions.querySelector('.suggestions-list');
                    if (oldSuggestions) {
                        oldSuggestions.remove();
                    }

                    const userToken = this.getUserToken();
                    const timestamp = Date.now();
                    const currentQuery = query.trim();

                    // При празно поле - максимум 10 резултата
                    const limit = currentQuery === '' ? 10 : 1000;

                    let fetchUrl = `index.php?route=catalog/category/autocomplete&filter_name=${encodeURIComponent(currentQuery)}&limit=${limit}&user_token=${userToken}&_=${timestamp}`;

                    fetch(fetchUrl, {
                        signal: controller.signal,
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Грешка при зареждане на категории');
                        return response.json();
                    })
                    .then(data => {
                        loadingIndicator.style.display = 'none';

                        const suggestionsList = document.createElement('div');
                        suggestionsList.className = 'suggestions-list absolute w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 z-10 max-h-60 overflow-y-auto';

                        if (data.length > 0) {
                            data.forEach(category => {
                                const item = document.createElement('div');
                                item.className = 'p-2 cursor-pointer hover:bg-gray-100 autocomplete-suggestion';
                                item.textContent = category.name;
                                item.dataset.id = category.category_id;
                                item.addEventListener('click', () => {
                                    this.selectPromotionCategory(category.category_id, category.name);
                                });
                                suggestionsList.appendChild(item);
                            });
                        } else {
                            const noResults = document.createElement('div');
                            noResults.className = 'p-2 text-gray-500 autocomplete-no-results';
                            noResults.textContent = currentQuery ? 'Няма намерени категории' : 'Няма предложения за категории';
                            suggestionsList.appendChild(noResults);
                        }

                        categorySuggestions.appendChild(suggestionsList);
                        categorySuggestions.classList.remove('hidden');
                    })
                    .catch(error => {
                        loadingIndicator.style.display = 'none';
                        if (error.name !== 'AbortError') {
                            console.error('Грешка при зареждане на категории:', error);
                        }
                    });
                };

                // Event listeners за input полето
                categoryInput.addEventListener('input', (e) => {
                    const query = e.target.value;
                    
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        fetchAndDisplaySuggestions(query);
                    }, 300);
                });

                categoryInput.addEventListener('focus', () => {
                    const query = categoryInput.value;
                    fetchAndDisplaySuggestions(query);
                });

                // Скриваме предложенията при клик извън тях
                document.addEventListener('click', (e) => {
                    if (!categoryInput.contains(e.target) && !categorySuggestions.contains(e.target)) {
                        categorySuggestions.classList.add('hidden');
                    }
                });

                categoryInput.dataset.listenerAttached = 'true';
            },

            /**
             * Избира категория от автодопълването за промоции
             */
            selectPromotionCategory: function(categoryId, categoryName) {
                const categoryInput = document.getElementById('promotion-category');
                const categorySuggestions = document.getElementById('promotion-category-suggestions');
                const categoryIdInput = document.getElementById('promotion-category-id');

                if (categoryInput) {
                    categoryInput.value = categoryName;
                }
                if (categoryIdInput) {
                    categoryIdInput.value = categoryId;
                }
                if (categorySuggestions) {
                    categorySuggestions.classList.add('hidden');
                }
            },

            /**
             * Отваряне на промоция модал
             */
            openPromotionModal: function(modal) {
                if (modal) {
                    // Изчистваме формата
                    this.clearPromotionForm();
                    
                    modal.classList.remove('hidden');
                    modal.classList.add('flex');
                    document.body.style.overflow = 'hidden';
                    
                    // Задаваме минимална дата за днес
                    const today = new Date().toISOString().split('T')[0];
                    const startDateInput = document.getElementById('promotion-date-start');
                    const endDateInput = document.getElementById('promotion-date-end');
                    
                    if (startDateInput) {
                        startDateInput.min = today;
                        startDateInput.value = today;
                    }
                    if (endDateInput) {
                        endDateInput.min = today;
                    }
                }
            },

            /**
             * Затваряне на промоция модал
             */
            closePromotionModal: function(modal) {
                if (modal) {
                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    document.body.style.overflow = '';
                    
                    // Изчистваме формата
                    this.clearPromotionForm();
                }
            },

            /**
             * Изчистване на промоция формата
             */
            clearPromotionForm: function() {
                const form = document.getElementById('promotion-form');
                if (form) {
                    form.reset();
                }

                // Изчистваме скритите полета
                const categoryIdInput = document.getElementById('promotion-category-id');
                if (categoryIdInput) {
                    categoryIdInput.value = '';
                }

                // Изчистваме избраните продукти
                this.selectedProducts = [];
                this.updateSelectedProductsList();
            },

            /**
             * Инициализация на автодопълването за продукти
             */
            initPromotionProductAutocomplete: function() {
                const productInput = document.getElementById('promotion-product-search');
                const productSuggestions = document.getElementById('promotion-product-suggestions');

                if (!productInput || !productSuggestions) {
                    return;
                }

                // Проверяваме дали вече има event listeners
                if (productInput.dataset.listenerAttached === 'true') {
                    return;
                }

                let debounceTimer;
                let currentRequest = null;

                const fetchAndDisplayProductSuggestions = (query) => {
                    if (query.trim().length < 2) {
                        productSuggestions.classList.add('hidden');
                        return;
                    }

                    // Отменяме предишната заявка, ако има такава
                    if (currentRequest) {
                        currentRequest.abort();
                    }

                    const controller = new AbortController();
                    currentRequest = controller;

                    productSuggestions.classList.remove('hidden');

                    // Изчистваме старите предложения
                    productSuggestions.innerHTML = '<div class="p-2 text-gray-500 text-sm">Зареждане...</div>';

                    const userToken = this.getUserToken();
                    const timestamp = Date.now();

                    let fetchUrl = `index.php?route=catalog/product/autocomplete&type=product&filter_name=${encodeURIComponent(query)}&user_token=${userToken}&_=${timestamp}`;

                    fetch(fetchUrl, {
                        signal: controller.signal,
                        cache: 'no-store',
                        headers: { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache', 'Expires': '0' }
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Грешка при зареждане на продукти');
                        return response.json();
                    })
                    .then(data => {
                        productSuggestions.innerHTML = '';

                        const suggestionsList = document.createElement('div');
                        suggestionsList.className = 'absolute w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 z-10 max-h-60 overflow-y-auto';

                        if (data.length > 0) {
                            data.forEach(product => {
                                // Проверяваме дали продуктът вече е избран
                                const isSelected = this.selectedProducts.some(p => p.product_id === product.product_id);

                                if (!isSelected) {
                                    const item = document.createElement('div');
                                    item.className = 'p-2 cursor-pointer hover:bg-gray-100 flex items-center space-x-2';

                                    item.innerHTML = `
                                        <img src="${product.thumb}" alt="${product.name}" class="w-8 h-8 object-cover rounded">
                                        <div class="flex-1">
                                            <div class="text-sm font-medium">${product.name}</div>
                                            <div class="text-xs text-gray-500">${product.model}</div>
                                        </div>
                                    `;

                                    item.addEventListener('click', () => {
                                        this.addProductToPromotion(product);
                                        productInput.value = '';
                                        productSuggestions.classList.add('hidden');
                                    });

                                    suggestionsList.appendChild(item);
                                }
                            });

                            if (suggestionsList.children.length === 0) {
                                const noResults = document.createElement('div');
                                noResults.className = 'p-2 text-gray-500';
                                noResults.textContent = 'Всички намерени продукти са вече избрани';
                                suggestionsList.appendChild(noResults);
                            }
                        } else {
                            const noResults = document.createElement('div');
                            noResults.className = 'p-2 text-gray-500';
                            noResults.textContent = 'Няма намерени продукти';
                            suggestionsList.appendChild(noResults);
                        }

                        productSuggestions.appendChild(suggestionsList);
                    })
                    .catch(error => {
                        if (error.name !== 'AbortError') {
                            console.error('Грешка при зареждане на продукти:', error);
                            productSuggestions.innerHTML = '<div class="p-2 text-red-500 text-sm">Грешка при зареждане</div>';
                        }
                    });
                };

                // Event listeners за input полето
                productInput.addEventListener('input', (e) => {
                    const query = e.target.value;

                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        fetchAndDisplayProductSuggestions(query);
                    }, 300);
                });

                // Скриваме предложенията при клик извън тях
                document.addEventListener('click', (e) => {
                    if (!productInput.contains(e.target) && !productSuggestions.contains(e.target)) {
                        productSuggestions.classList.add('hidden');
                    }
                });

                productInput.dataset.listenerAttached = 'true';
            },

            /**
             * Добавя продукт към промоцията
             */
            addProductToPromotion: function(product) {
                // Проверяваме дали продуктът вече е добавен
                const exists = this.selectedProducts.some(p => p.product_id === product.product_id);
                if (exists) {
                    return;
                }

                this.selectedProducts.push(product);
                this.updateSelectedProductsList();
            },

            /**
             * Премахва продукт от промоцията
             */
            removeProductFromPromotion: function(productId) {
                this.selectedProducts = this.selectedProducts.filter(p => p.product_id !== productId);
                this.updateSelectedProductsList();
            },

            /**
             * Обновява списъка с избрани продукти
             */
            updateSelectedProductsList: function() {
                const countElement = document.getElementById('selected-products-count');
                const listElement = document.getElementById('selected-products-list');

                if (countElement) {
                    const count = this.selectedProducts.length;
                    countElement.textContent = count === 1 ? '1 продукт' : `${count} продукта`;
                }

                if (listElement) {
                    listElement.innerHTML = '';

                    if (this.selectedProducts.length === 0) {
                        const emptyMessage = document.createElement('div');
                        emptyMessage.className = 'text-gray-500 text-sm text-center py-4';
                        emptyMessage.textContent = 'Няма избрани продукти';
                        listElement.appendChild(emptyMessage);
                    } else {
                        this.selectedProducts.forEach(product => {
                            const productCard = document.createElement('div');
                            productCard.className = 'flex items-center space-x-2 p-2 bg-gray-50 rounded border';

                            productCard.innerHTML = `
                                <img src="${product.thumb}" alt="${product.name}" class="w-10 h-10 object-cover rounded">
                                <div class="flex-1">
                                    <div class="text-sm font-medium">${product.name}</div>
                                    <div class="text-xs text-gray-500">${product.model}</div>
                                </div>
                                <button type="button" class="text-red-500 hover:text-red-700 p-1" onclick="BackendModule.removeProductFromPromotion('${product.product_id}')">
                                    <i class="ri-close-line"></i>
                                </button>
                            `;

                            listElement.appendChild(productCard);
                        });
                    }
                }
            },

            /**
             * Настройка на промоция формата
             */
            setupPromotionForm: function() {
                const form = document.getElementById('promotion-form');

                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handlePromotionSubmit();
                    });
                }

                // Валидация на датите
                const startDateInput = document.getElementById('promotion-date-start');
                const endDateInput = document.getElementById('promotion-date-end');

                if (startDateInput && endDateInput) {
                    startDateInput.addEventListener('change', () => {
                        endDateInput.min = startDateInput.value;
                        if (endDateInput.value && endDateInput.value < startDateInput.value) {
                            endDateInput.value = startDateInput.value;
                        }
                    });
                }
            },

            /**
             * Обработва изпращането на промоция формата
             */
            handlePromotionSubmit: function() {
                const form = document.getElementById('promotion-form');
                const submitButton = document.getElementById('apply-promotion');

                if (!form || !submitButton) {
                    return;
                }

                // Валидация
                if (!this.validatePromotionForm()) {
                    return;
                }

                // Показваме loading състояние
                const originalButtonText = submitButton.innerHTML;
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="ri-loader-4-line ri-spin"></i> Прилагане...';

                // Подготвяме данните
                const formData = new FormData(form);

                // Добавяме избраните продукти
                this.selectedProducts.forEach(product => {
                    formData.append('selected_products[]', product.product_id);
                });

                // Добавяме user_token
                const userToken = this.getUserToken();
                if (userToken) {
                    formData.append('user_token', userToken);
                }

                // Изпращаме AJAX заявка
                fetch('index.php?route=catalog/product/promotion', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.showSuccessMessage(data.success);
                        this.closePromotionModal(document.getElementById('promotion-modal'));

                        // Презареждаме страницата след кратка пауза
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else if (data.error) {
                        this.showErrorMessage(data.error);
                    }
                })
                .catch(error => {
                    console.error('Грешка при изпращане на промоцията:', error);
                    this.showErrorMessage('Възникна грешка при прилагане на промоцията');
                })
                .finally(() => {
                    // Възстановяваме бутона
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalButtonText;
                });
            },

            /**
             * Валидира промоция формата
             */
            validatePromotionForm: function() {
                const percentage = document.getElementById('promotion-percentage').value;
                const startDate = document.getElementById('promotion-date-start').value;
                const endDate = document.getElementById('promotion-date-end').value;
                const categoryId = document.getElementById('promotion-category-id').value;

                // Проверка на процента
                if (!percentage || parseFloat(percentage) <= 0 || parseFloat(percentage) > 100) {
                    this.showErrorMessage('Моля въведете валиден процент между 0 и 100');
                    return false;
                }

                // Проверка на датите
                if (!startDate || !endDate) {
                    this.showErrorMessage('Моля въведете начална и крайна дата');
                    return false;
                }

                if (new Date(startDate) >= new Date(endDate)) {
                    this.showErrorMessage('Крайната дата трябва да бъде след началната');
                    return false;
                }

                // Проверка дали има избрана категория или продукти
                if (!categoryId && this.selectedProducts.length === 0) {
                    this.showErrorMessage('Моля изберете категория или конкретни продукти');
                    return false;
                }

                return true;
            },

            /**
             * Показва съобщение за успех
             */
            showSuccessMessage: function(message) {
                // TODO: Имплементиране на toast notification или друг UI компонент
                alert(message);
            },

            /**
             * Показва съобщение за грешка
             */
            showErrorMessage: function(message) {
                // TODO: Имплементиране на toast notification или друг UI компонент
                alert(message);
            }
        });
    }
})();
