<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto overflow-x-hidden bg-gray-50" style="padding-bottom: 200px">

    <!-- Product Header -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Продукти</h1>
                <p class="text-gray-500 mt-1">Управление на всички продукти в магазина</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-2">
                <button id="promotion-btn" class="px-4 py-2 bg-orange-500 text-white rounded-button hover:bg-orange-600 transition-colors whitespace-nowrap flex items-center !rounded-button">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-percent-line"></i>
                    </div>
                    <span>Задай промоция</span>
                </button>
                <a href="{{ add_new_url }}" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-add-line"></i>
                    </div>
                    <span>Нов продукт</span>
                </a>
            </div>
        </div>
    </div>
    <!-- Filters -->
    <div class="bg-white border-b border-gray-200 px-6 py-3">
        <div class="flex flex-wrap items-center gap-4">
            <button id="filter-btn" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button relative" {% if has_active_filters %}title="Приложени филтри"{% endif %}>
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-filter-3-line"></i>
                </div>
                <span>Филтър</span>
                {% if has_active_filters %}
                <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white"></div>
                {% endif %}
            </button>
            <div class="w-full md:w-auto">
                <div class="relative">
                    <!-- Filter Modal -->
                    <div id="filter-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
                        <div class="bg-white rounded-lg w-full max-w-md mx-4">
                            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-800">Филтър</h3>
                                <button id="close-filter" class="text-gray-400 hover:text-gray-500">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-close-line"></i>
                                    </div>
                                </button>
                            </div>
                            <div class="p-6">
                                <form id="filter-form" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Категория</label>
                                        <div class="relative">
                                            <input type="text" id="filter-category" name="filter_category" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете категория" value="{{ filter_category_name|default('') }}">
                                            <input type="hidden" id="filter-category-id" name="filter_category_id" value="{{ filter_category_id|default('') }}">
                                            <div id="filter-category-suggestions" class="hidden"></div>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Име на продукт</label>
                                        <input type="text" name="filter_name" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете име на продукт" value="{{ filter_name|default('') }}">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Модел</label>
                                        <input type="text" name="filter_model" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете модел" value="{{ filter_model|default('') }}">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Цена</label>
                                        <div class="flex space-x-2">
                                            <input type="number" name="filter_price_min" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="От" value="{{ filter_price_min|default('') }}" step="0.01">
                                            <input type="number" name="filter_price_max" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="До" value="{{ filter_price_max|default('') }}" step="0.01">
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
                                        <select name="filter_status" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
                                            <option value="">Всички</option>
                                            <option value="1" {% if filter_status == '1' %}selected{% endif %}>Активен</option>
                                            <option value="0" {% if filter_status == '0' %}selected{% endif %}>Неактивен</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Период</label>
                                        <div class="flex space-x-2">
                                            <input type="date" name="filter_date_start" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" value="{{ filter_date_start|default('') }}">
                                            <input type="date" name="filter_date_end" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" value="{{ filter_date_end|default('') }}">
                                        </div>
                                    </div>
                                    <div class="flex justify-end space-x-2 mt-6">
                                        <button type="button" id="reset-filter" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Изчисти</button>
                                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи филтър</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div id="sort-dropdown" class="hidden absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded shadow-lg">
                        <ul class="py-1">
                            {% for sort_option in sort_options %}
                            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" data-sort="{{ sort_option.value }}">{{ sort_option.text }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">Активни:</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="filter-active" {% if filter_active %}checked{% endif %}>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">Промоции:</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="filter-special" {% if filter_special %}checked{% endif %}>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="flex items-center space-x-2 ml-auto">
                <button class="w-10 h-10 flex items-center justify-center rounded-button text-gray-600 hover:bg-gray-200 {% if view_type == 'grid' %} bg-gray-100 active {% endif %}" id="grid-view">
                    <div class="w-5 h-5 flex items-center justify-center">
                        <i class="ri-grid-line"></i>
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-button text-gray-600 hover:bg-gray-200 {% if view_type == 'list' %} bg-gray-100 active {% endif %}" id="list-view">
                    <div class="w-5 h-5 flex items-center justify-center">
                        <i class="ri-list-check"></i>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Promotion Modal -->
    <div id="promotion-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
        <div class="bg-white rounded-lg w-[90%] lg:w-[60%] 2xl:w-1/2 max-w-none h-[85vh] mx-4 flex flex-col">
            <!-- Фиксирана горна част -->
            <div class="flex justify-between items-center p-6 border-b border-gray-200 flex-shrink-0">
                <h3 class="text-lg font-semibold text-gray-800">Задаване на промоция</h3>
                <button id="close-promotion" class="text-gray-400 hover:text-gray-500">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-close-line"></i>
                    </div>
                </button>
            </div>
            <!-- Фиксирани полета (без скролиране) -->
            <div class="p-6 space-y-4 flex-shrink-0 border-b border-gray-100">
                <form id="promotion-form" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Категория</label>
                        <div class="relative">
                            <input type="text" id="promotion-category" name="promotion_category" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете категория (незадължително)">
                            <input type="hidden" id="promotion-category-id" name="promotion_category_id">
                            <div id="promotion-category-suggestions" class="hidden"></div>
                        </div>
                        <div id="selected-category-card" class="hidden mt-2">
                            <!-- Избраната категория ще се показва тук като карта -->
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Оставете празно за промоция само на избраните продукти</p>
                    </div>

                    <!-- Компактен layout за стойност и период -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Стойност</label>
                            <div class="flex space-x-2">
                                <input type="number" id="promotion-value" name="promotion_value" class="flex-1 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете стойност" min="0" step="0.01" required>
                                <select id="promotion-type" name="promotion_type" class="px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm bg-white">
                                    <option value="percentage">%</option>
                                    <option value="fixed">лв.</option>
                                </select>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Въведете процент отстъпка (0-100) или фиксирана сума в лева</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Период на промоцията</label>
                            <div class="flex space-x-2">
                                <input type="date" id="promotion-date-start" name="promotion_date_start" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" required>
                                <input type="date" id="promotion-date-end" name="promotion_date_end" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" required>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Изберете начална и крайна дата</p>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Скролираща част с продукти -->
            <div class="flex-1 overflow-y-auto min-h-[300px]">
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Търсене на продукти</label>
                        <div class="relative">
                            <input type="text" id="promotion-product-search" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="Въведете име на продукт">
                            <div id="promotion-product-suggestions" class="hidden"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Добавете конкретни продукти към промоцията</p>
                    </div>

                    <div id="selected-products" class="space-y-2 p-3 bg-blue-25 border border-dashed border-blue-200 rounded-md">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Избрани продукти</span>
                            <span id="selected-products-count" class="text-xs text-gray-500">0 продукта</span>
                        </div>
                        <div id="selected-products-list" class="space-y-2 max-h-40 overflow-y-auto">
                            <!-- Избраните продукти ще се показват тук -->
                        </div>
                    </div>

                </div>
            </div>

            <!-- Фиксирани бутони в дъното -->
            <div class="p-6 border-t border-gray-200 flex-shrink-0">
                <div class="flex justify-end space-x-2">
                    <button type="button" id="cancel-promotion" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Отказ</button>
                    <button type="submit" form="promotion-form" id="apply-promotion" class="px-4 py-2 bg-orange-500 text-white rounded-button hover:bg-orange-600 text-sm whitespace-nowrap">Приложи</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 p-6" id="products-grid">
        {% if products %}
            {% for product in products %}
            <!-- Product Card -->
            <div class="product-card bg-white rounded shadow hover:shadow-md transition-all flex flex-col">
                <div class="relative flex-shrink-0">
                    <a href="{{ product.edit }}">
                    <div class="product-image-container w-full h-48 rounded-t bg-gray-100 flex items-center justify-center relative" data-product-id="{{ product.product_id }}" data-width="400" data-height="300">
                        <div class="product-image-placeholder">
                            <div class="w-10 h-10 text-gray-300 animate-spin">
                                <i class="ri-loader-4-line ri-2x"></i>
                            </div>
                        </div>
                        <div class="product-image-error hidden">
                            <div class="w-10 h-10 text-red-500">
                                <i class="ri-error-warning-line ri-2x"></i>
                            </div>
                            <p class="text-sm text-red-500 mt-2">Грешка при зареждане</p>
                        </div>
                        <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="{{ product.name }}" class="w-full h-48 object-cover object-top rounded-t absolute inset-0 opacity-0 transition-opacity duration-300">
                    </div>
                    </a>
                    <div class="absolute top-3 right-3 flex space-x-2">
                        {% if product.status %}
                        <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">Активен</span>
                        {% else %}
                        <span class="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full">Неактивен</span>
                        {% endif %}

                        {% if product.special %}
                        <span class="px-2 py-1 bg-red-500 text-white text-xs rounded-full">{{ product.discount_percent }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="product-content p-4 flex-1">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h3 class="font-medium text-gray-800">{{ product.name }}</h3>
                            <p class="text-sm text-gray-500">{{ product.category }}</p>
                            {% if product.special %}
                            <p class="font-bold text-red-600">{{ product.price|number_format(2, '.', ',') }} лв.</p>
                            <p class="text-sm text-gray-500 line-through">{{ product.old_price|number_format(2, '.', ',') }} лв.</p>
                            {% else %}
                            <p class="font-bold text-gray-800">{{ product.price|number_format(2, '.', ',') }} лв.</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-4">
                        <span class="text-sm text-gray-500">Код: {{ product.model }}</span>
                        <div class="card-actions flex space-x-1">
                            <a href="{{ product.edit }}" class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-edit-line"></i>
                                </div>
                            </a>
                              <div class="relative">
                                <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full product-actions-btn">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-more-2-line"></i>
                                    </div>
                                </button>
                                <div class="product-actions-dropdown hidden absolute right-0 bottom-full mb-2 bg-white border border-gray-200 rounded shadow-lg z-10 w-40">
                                    <ul class="py-1">
                                        <li>
                                            <a href="#" class="px-4 py-2 hover:bg-gray-100 flex items-center text-sm duplicate-product" data-product-id="{{ product.product_id }}">
                                                <i class="ri-file-copy-line mr-2"></i> Дублиране
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="px-4 py-2 hover:bg-gray-100 flex items-center text-sm delete-product" data-product-id="{{ product.product_id }}">
                                                <i class="ri-delete-bin-5-line text-red-500 mr-2"></i> Изтриване
                                            </a>
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-span-full text-center py-8">
                <div class="w-16 h-16 mx-auto mb-4 text-gray-300">
                    <i class="ri-shopping-bag-line ri-3x"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-800 mb-1">Няма намерени продукти</h3>
                <p class="text-gray-500">Все още няма добавени продукти или няма продукти, отговарящи на зададените критерии.</p>
                <a href="{{ add_new_url }}" class="mt-4 inline-flex items-center px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90">
                    <i class="ri-add-line mr-2"></i> Нов продукт
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    <div class="p-6">
    {{ pagination_html|raw }}
    </div>
</main>


<!-- Глобални променливи за JavaScript -->
<script type="text/javascript">
    // URL за изтриване на продукт
    var deleteUrl = '{{ delete_url|raw }}';
</script>
